/**
 * Simple test to verify image tools are properly registered and working
 */

import { registerImageTools } from "./dist/tools/image-tools.js";

// Create a mock server to test tool registration
const mockServer = {
  tools: [],
  tool: function(name, description, schema, handler) {
    this.tools.push({ name, description, schema, handler });
    console.log(`✅ Registered tool: ${name}`);
  }
};

// Test image tools registration
console.log("🧪 Testing Image Tools Registration...\n");

try {
  registerImageTools(mockServer);
  
  console.log(`\n📊 Successfully registered ${mockServer.tools.length} image tools:`);
  mockServer.tools.forEach((tool, index) => {
    console.log(`${index + 1}. ${tool.name} - ${tool.description}`);
  });
  
  // Verify core image tools are present
  const expectedTools = [
    'find_and_insert_image',
    'search_and_insert_image', 
    'context_aware_image_insertion',
    'insert_image_from_url',
    'replace_image',
    'get_image_metadata',
    'smart_replace_images'
  ];
  
  console.log("\n🔍 Checking for core image tools:");
  expectedTools.forEach(toolName => {
    const found = mockServer.tools.find(tool => tool.name === toolName);
    if (found) {
      console.log(`✅ ${toolName} - FOUND`);
    } else {
      console.log(`❌ ${toolName} - MISSING`);
    }
  });
  
  console.log("\n🎉 Image tools test completed successfully!");
  console.log("📝 All image tools are properly registered and ready for automatic image insertion.");
  
} catch (error) {
  console.error("❌ Error testing image tools:", error);
  process.exit(1);
}
