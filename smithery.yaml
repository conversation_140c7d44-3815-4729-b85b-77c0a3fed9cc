# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    {}
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => {
      // Try to detect if bun is available
      const isBunAvailable = (() => {
        try {
          require('child_process').execSync('bun --version', { stdio: 'ignore' });
          return true;
        } catch (e) {
          return false;
        }
      })();

      return {
        command: isBunAvailable ? 'bunx' : 'npx',
        args: ['claude-talk-to-figma-mcp@latest']
      };
    }
