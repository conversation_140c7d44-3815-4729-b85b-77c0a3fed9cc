import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { sendCommandToFigma } from "../utils/websocket.js";

/**
 * Simplified smart design tool for testing automatic image insertion
 */
export function registerSimpleSmartDesign(server: McpServer): void {
  server.tool(
    "test_smart_design",
    "Test automatic image insertion with a simple e-commerce design",
    {
      productType: z.string().optional().describe("Type of product (e.g., 'electronics', 'fashion', 'food')"),
      includeImages: z.boolean().optional().describe("Whether to include automatic images (default: true)")
    },
    async ({ productType = "electronics", includeImages = true }) => {
      try {
        console.log("Creating test smart design for:", productType);
        
        // Step 1: Create main frame
        const frameResult = await sendCommandToFigma("create_frame", {
          x: 0,
          y: 0,
          width: 1200,
          height: 800,
          name: `${productType} Product Page`,
          fills: [{ type: "SOLID", color: { r: 1, g: 1, b: 1 } }]
        }) as any;
        
        const frameId = frameResult.id;
        console.log("Main frame created:", frameId);
        
        // Step 2: Create header
        const headerResult = await sendCommandToFigma("create_frame", {
          x: 0,
          y: 0,
          width: 1200,
          height: 80,
          name: "Header",
          parentId: frameId,
          fills: [{ type: "SOLID", color: { r: 0.95, g: 0.95, b: 0.95 } }]
        }) as any;
        
        // Step 3: Add title
        await sendCommandToFigma("create_text", {
          x: 20,
          y: 25,
          text: `${productType.toUpperCase()} PRODUCT SHOWCASE`,
          fontSize: 24,
          fontWeight: "BOLD",
          parentId: frameId
        });
        
        // Step 4: Create product section
        const productSectionResult = await sendCommandToFigma("create_frame", {
          x: 50,
          y: 120,
          width: 1100,
          height: 600,
          name: "Product Section",
          parentId: frameId,
          fills: [{ type: "SOLID", color: { r: 0.98, g: 0.98, b: 0.98 } }]
        }) as any;
        
        const insertedImages = [];
        
        // Step 5: Insert images if enabled
        if (includeImages) {
          console.log("Starting automatic image insertion...");
          
          // Hero product image
          try {
            const heroSearchResult = await sendCommandToFigma("search_images", {
              query: `${productType} product showcase professional`,
              imageType: "PHOTO",
              maxResults: 1
            }) as any;
            
            if (heroSearchResult.images && heroSearchResult.images.length > 0) {
              const heroInsertResult = await sendCommandToFigma("insert_image_from_url", {
                imageUrl: heroSearchResult.images[0].url,
                x: 100,
                y: 180,
                width: 400,
                height: 300,
                name: "Hero Product Image",
                parentId: frameId,
                scaleMode: "FIT"
              }) as any;
              
              insertedImages.push({
                type: "hero",
                nodeId: heroInsertResult.id,
                description: "Main product hero image"
              });
              console.log("Hero image inserted successfully");
            }
          } catch (error) {
            console.warn("Failed to insert hero image:", error);
          }
          
          // Product gallery images
          try {
            const gallerySearchResult = await sendCommandToFigma("search_images", {
              query: `${productType} product details multiple angles`,
              imageType: "PHOTO",
              maxResults: 3
            }) as any;
            
            if (gallerySearchResult.images && gallerySearchResult.images.length > 0) {
              for (let i = 0; i < Math.min(3, gallerySearchResult.images.length); i++) {
                const galleryInsertResult = await sendCommandToFigma("insert_image_from_url", {
                  imageUrl: gallerySearchResult.images[i].url,
                  x: 550 + (i * 180),
                  y: 180,
                  width: 150,
                  height: 150,
                  name: `Gallery Image ${i + 1}`,
                  parentId: frameId,
                  scaleMode: "FIT"
                }) as any;
                
                insertedImages.push({
                  type: "gallery",
                  nodeId: galleryInsertResult.id,
                  description: `Product gallery image ${i + 1}`
                });
              }
              console.log("Gallery images inserted successfully");
            }
          } catch (error) {
            console.warn("Failed to insert gallery images:", error);
          }
          
          // Feature icons
          try {
            const iconSearchResult = await sendCommandToFigma("search_images", {
              query: `${productType} feature icons`,
              imageType: "ICON",
              maxResults: 4
            }) as any;
            
            if (iconSearchResult.images && iconSearchResult.images.length > 0) {
              for (let i = 0; i < Math.min(4, iconSearchResult.images.length); i++) {
                const iconInsertResult = await sendCommandToFigma("insert_image_from_url", {
                  imageUrl: iconSearchResult.images[i].url,
                  x: 100 + (i * 100),
                  y: 520,
                  width: 48,
                  height: 48,
                  name: `Feature Icon ${i + 1}`,
                  parentId: frameId,
                  scaleMode: "FIT"
                }) as any;
                
                insertedImages.push({
                  type: "icon",
                  nodeId: iconInsertResult.id,
                  description: `Feature icon ${i + 1}`
                });
              }
              console.log("Feature icons inserted successfully");
            }
          } catch (error) {
            console.warn("Failed to insert feature icons:", error);
          }
        }
        
        // Step 6: Add product details text
        await sendCommandToFigma("create_text", {
          x: 100,
          y: 350,
          text: `Premium ${productType} Product\n\n✓ High Quality\n✓ Fast Shipping\n✓ 30-Day Returns\n✓ Customer Support`,
          fontSize: 16,
          parentId: frameId
        });
        
        return {
          content: [
            {
              type: "text",
              text: `✅ Successfully created ${productType} product page with ${insertedImages.length} automatically inserted images!`
            },
            {
              type: "text",
              text: `Frame ID: ${frameId}`
            },
            {
              type: "text",
              text: `Images inserted: ${insertedImages.map(img => img.type).join(", ")}`
            }
          ],
          structuredContent: {
            frameId,
            productType,
            insertedImages,
            includeImages
          }
        };
        
      } catch (error) {
        console.error("Error in test_smart_design:", error);
        return {
          content: [
            {
              type: "text",
              text: `❌ Error creating smart design: ${error instanceof Error ? error.message : String(error)}`
            }
          ],
          structuredContent: {
            error: {
              message: error instanceof Error ? error.message : String(error),
              operation: "creating test smart design"
            }
          },
          isError: true
        };
      }
    }
  );
}
